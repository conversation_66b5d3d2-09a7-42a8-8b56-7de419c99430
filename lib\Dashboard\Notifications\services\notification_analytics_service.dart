import '../models/notification_isar.dart';
import '../models/notification_status.dart';
import '../models/notification_priority.dart';

/// Pure analytics service for Notifications module - no state, just calculations
/// Following the cattle analytics service pattern: single-pass O(n) efficiency
/// All methods are static and purely functional
class NotificationAnalyticsService {

  /// Calculate notification analytics from all notification data
  /// Single entry point with O(n) efficiency across all notifications
  static NotificationAnalyticsResult calculate(List<NotificationIsar> notifications) {
    if (notifications.isEmpty) {
      return NotificationAnalyticsResult.empty;
    }

    // Single pass through all data for maximum efficiency
    final accumulator = _NotificationAnalyticsAccumulator();

    for (final notification in notifications) {
      accumulator.process(notification);
    }

    return accumulator.toResult();
  }

  /// Calculate delivery rate analytics
  static Map<String, double> calculateDeliveryRates(List<NotificationIsar> notifications) {
    if (notifications.isEmpty) {
      return {
        'pushDeliveryRate': 0.0,
        'emailDeliveryRate': 0.0,
        'smsDeliveryRate': 0.0,
        'overallDeliveryRate': 0.0,
      };
    }

    int totalNotifications = notifications.length;
    int pushSent = 0;
    int emailSent = 0;
    int smsSent = 0;
    int overallDelivered = 0;

    for (final notification in notifications) {
      if (notification.pushNotificationSent) {
        pushSent++;
      }
      // TODO: Add email and SMS tracking when implemented
      
      // Count as delivered if any delivery method succeeded
      if (notification.pushNotificationSent) {
        overallDelivered++;
      }
    }

    return {
      'pushDeliveryRate': totalNotifications > 0 ? (pushSent / totalNotifications * 100) : 0.0,
      'emailDeliveryRate': 0.0, // TODO: Implement when email tracking is added
      'smsDeliveryRate': 0.0,   // TODO: Implement when SMS tracking is added
      'overallDeliveryRate': totalNotifications > 0 ? (overallDelivered / totalNotifications * 100) : 0.0,
    };
  }

  /// Calculate read rate analytics
  static Map<String, double> calculateReadRates(List<NotificationIsar> notifications) {
    if (notifications.isEmpty) {
      return {
        'overallReadRate': 0.0,
        'averageTimeToRead': 0.0,
      };
    }

    int totalNotifications = notifications.length;
    int readNotifications = 0;
    double totalTimeToRead = 0.0;
    int notificationsWithReadTime = 0;

    for (final notification in notifications) {
      if (notification.status == NotificationStatus.read && notification.readAt != null) {
        readNotifications++;
        
        // Calculate time to read if both created and read times are available
        if (notification.createdAt != null) {
          final timeToRead = notification.readAt!.difference(notification.createdAt!).inMinutes.toDouble();
          totalTimeToRead += timeToRead;
          notificationsWithReadTime++;
        }
      }
    }

    return {
      'overallReadRate': totalNotifications > 0 ? (readNotifications / totalNotifications * 100) : 0.0,
      'averageTimeToRead': notificationsWithReadTime > 0 ? (totalTimeToRead / notificationsWithReadTime) : 0.0,
    };
  }

  /// Calculate category performance analytics
  static Map<String, Map<String, dynamic>> calculateCategoryPerformance(List<NotificationIsar> notifications) {
    if (notifications.isEmpty) {
      return {};
    }

    final categoryStats = <String, _CategoryStats>{};

    // Collect stats for each category
    for (final notification in notifications) {
      final category = notification.category ?? 'uncategorized';
      
      if (!categoryStats.containsKey(category)) {
        categoryStats[category] = _CategoryStats();
      }
      
      categoryStats[category]!.process(notification);
    }

    // Convert to result format
    final result = <String, Map<String, dynamic>>{};
    
    categoryStats.forEach((category, stats) {
      result[category] = {
        'totalCount': stats.totalCount,
        'unreadCount': stats.unreadCount,
        'readCount': stats.readCount,
        'readRate': stats.totalCount > 0 ? (stats.readCount / stats.totalCount * 100) : 0.0,
        'averageTimeToRead': stats.readCount > 0 ? (stats.totalTimeToRead / stats.readCount) : 0.0,
        'deliveryRate': stats.totalCount > 0 ? (stats.deliveredCount / stats.totalCount * 100) : 0.0,
      };
    });

    return result;
  }

  /// Calculate priority distribution analytics
  static Map<NotificationPriority, int> calculatePriorityDistribution(List<NotificationIsar> notifications) {
    final distribution = <NotificationPriority, int>{
      NotificationPriority.low: 0,
      NotificationPriority.medium: 0,
      NotificationPriority.high: 0,
      NotificationPriority.critical: 0,
    };

    for (final notification in notifications) {
      final priority = notification.priority;
      distribution[priority] = (distribution[priority] ?? 0) + 1;
    }

    return distribution;
  }

  /// Calculate time-based analytics (notifications per day/week/month)
  static Map<String, int> calculateTimeBasedAnalytics(List<NotificationIsar> notifications, String period) {
    if (notifications.isEmpty) {
      return {};
    }

    final timeGroups = <String, int>{};

    for (final notification in notifications) {
      if (notification.createdAt == null) continue;

      String timeKey;
      switch (period.toLowerCase()) {
        case 'day':
          timeKey = '${notification.createdAt!.year}-${notification.createdAt!.month.toString().padLeft(2, '0')}-${notification.createdAt!.day.toString().padLeft(2, '0')}';
          break;
        case 'week':
          final weekStart = notification.createdAt!.subtract(Duration(days: notification.createdAt!.weekday - 1));
          timeKey = '${weekStart.year}-W${((weekStart.dayOfYear - 1) / 7).floor() + 1}';
          break;
        case 'month':
          timeKey = '${notification.createdAt!.year}-${notification.createdAt!.month.toString().padLeft(2, '0')}';
          break;
        default:
          timeKey = notification.createdAt!.toIso8601String().split('T')[0];
      }

      timeGroups[timeKey] = (timeGroups[timeKey] ?? 0) + 1;
    }

    return timeGroups;
  }
}

/// Analytics result for notifications - immutable data class
class NotificationAnalyticsResult {
  final int totalCount;
  final int unreadCount;
  final int readCount;
  final int actionedCount;
  final int archivedCount;
  final int expiredCount;
  final Map<String, int> categoryCounts;
  final Map<NotificationStatus, int> statusCounts;
  final Map<NotificationPriority, int> priorityCounts;
  final double readRate;
  final double deliveryRate;
  final double averageTimeToRead;

  const NotificationAnalyticsResult({
    required this.totalCount,
    required this.unreadCount,
    required this.readCount,
    required this.actionedCount,
    required this.archivedCount,
    required this.expiredCount,
    required this.categoryCounts,
    required this.statusCounts,
    required this.priorityCounts,
    required this.readRate,
    required this.deliveryRate,
    required this.averageTimeToRead,
  });

  static const NotificationAnalyticsResult empty = NotificationAnalyticsResult(
    totalCount: 0,
    unreadCount: 0,
    readCount: 0,
    actionedCount: 0,
    archivedCount: 0,
    expiredCount: 0,
    categoryCounts: {},
    statusCounts: {},
    priorityCounts: {},
    readRate: 0.0,
    deliveryRate: 0.0,
    averageTimeToRead: 0.0,
  );
}

/// Single-pass accumulator for notification analytics - O(n) efficiency
class _NotificationAnalyticsAccumulator {
  int totalCount = 0;
  int unreadCount = 0;
  int readCount = 0;
  int actionedCount = 0;
  int archivedCount = 0;
  int expiredCount = 0;
  int deliveredCount = 0;
  double totalTimeToRead = 0.0;
  int notificationsWithReadTime = 0;

  Map<String, int> categoryCounts = {};
  Map<NotificationStatus, int> statusCounts = {};
  Map<NotificationPriority, int> priorityCounts = {};

  void process(NotificationIsar notification) {
    totalCount++;

    // Count by status
    final status = notification.status;
    statusCounts[status] = (statusCounts[status] ?? 0) + 1;

    switch (status) {
      case NotificationStatus.unread:
        unreadCount++;
        break;
      case NotificationStatus.read:
        readCount++;
        break;
      case NotificationStatus.actioned:
        actionedCount++;
        break;
      case NotificationStatus.archived:
        archivedCount++;
        break;
      case NotificationStatus.expired:
        expiredCount++;
        break;
      case NotificationStatus.delivered:
        deliveredCount++;
        break;
    }

    // Count by category
    final category = notification.category ?? 'uncategorized';
    categoryCounts[category] = (categoryCounts[category] ?? 0) + 1;

    // Count by priority
    final priority = notification.priority;
    priorityCounts[priority] = (priorityCounts[priority] ?? 0) + 1;

    // Calculate time to read
    if (notification.status == NotificationStatus.read && 
        notification.readAt != null && 
        notification.createdAt != null) {
      final timeToRead = notification.readAt!.difference(notification.createdAt!).inMinutes.toDouble();
      totalTimeToRead += timeToRead;
      notificationsWithReadTime++;
    }

    // Track delivery
    if (notification.pushNotificationSent) {
      deliveredCount++;
    }
  }

  NotificationAnalyticsResult toResult() {
    final readRate = totalCount > 0 ? (readCount / totalCount * 100) : 0.0;
    final deliveryRate = totalCount > 0 ? (deliveredCount / totalCount * 100) : 0.0;
    final averageTimeToRead = notificationsWithReadTime > 0 ? (totalTimeToRead / notificationsWithReadTime) : 0.0;

    return NotificationAnalyticsResult(
      totalCount: totalCount,
      unreadCount: unreadCount,
      readCount: readCount,
      actionedCount: actionedCount,
      archivedCount: archivedCount,
      expiredCount: expiredCount,
      categoryCounts: Map.unmodifiable(categoryCounts),
      statusCounts: Map.unmodifiable(statusCounts),
      priorityCounts: Map.unmodifiable(priorityCounts),
      readRate: readRate,
      deliveryRate: deliveryRate,
      averageTimeToRead: averageTimeToRead,
    );
  }
}

/// Helper class for category-specific statistics
class _CategoryStats {
  int totalCount = 0;
  int unreadCount = 0;
  int readCount = 0;
  int deliveredCount = 0;
  double totalTimeToRead = 0.0;

  void process(NotificationIsar notification) {
    totalCount++;

    if (notification.status == NotificationStatus.unread) {
      unreadCount++;
    } else if (notification.status == NotificationStatus.read) {
      readCount++;
      
      // Calculate time to read
      if (notification.readAt != null && notification.createdAt != null) {
        final timeToRead = notification.readAt!.difference(notification.createdAt!).inMinutes.toDouble();
        totalTimeToRead += timeToRead;
      }
    }

    if (notification.pushNotificationSent) {
      deliveredCount++;
    }
  }
}

/// Extension to add dayOfYear property to DateTime
extension DateTimeExtension on DateTime {
  int get dayOfYear {
    final firstDayOfYear = DateTime(year, 1, 1);
    return difference(firstDayOfYear).inDays + 1;
  }
}
