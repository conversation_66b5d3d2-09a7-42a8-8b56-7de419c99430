import 'dart:async';
import 'package:logging/logging.dart';
import 'package:get_it/get_it.dart';
import '../models/notification_isar.dart';
import '../models/notification_priority.dart';
import '../models/notification_status.dart';
import 'notification_repository.dart';
import 'notification_delivery_service.dart';

/// Service for scheduling and managing delayed notifications
class NotificationSchedulingService {
  static final Logger _logger = Logger('NotificationSchedulingService');
  final NotificationRepository _repository = GetIt.instance<NotificationRepository>();
  final NotificationDeliveryService _deliveryService = GetIt.instance<NotificationDeliveryService>();
  
  final Map<String, Timer> _scheduledTimers = {};
  
  /// Schedule a notification for future delivery
  Future<String> scheduleNotification({
    required String title,
    required String message,
    required DateTime scheduledTime,
    String? category,
    String? cattleId,
    NotificationPriority priority = NotificationPriority.medium,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      // Create notification with scheduled status
      final notification = NotificationIsar()
        ..businessId = _generateBusinessId()
        ..title = title
        ..message = message
        ..category = category ?? 'scheduled'
        ..cattleId = cattleId
        ..priority = priority
        ..status = NotificationStatus.scheduled
        ..scheduledTime = scheduledTime
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now();
      
      // Save to database
      await _repository.createNotification(notification);
      
      // Schedule the timer
      _scheduleTimer(notification);
      
      _logger.info('Scheduled notification: ${notification.businessId} for $scheduledTime');
      return notification.businessId!;
      
    } catch (e, stackTrace) {
      _logger.severe('Error scheduling notification: $e', e, stackTrace);
      rethrow;
    }
  }
  
  /// Cancel a scheduled notification
  Future<bool> cancelScheduledNotification(String businessId) async {
    try {
      // Cancel timer if exists
      final timer = _scheduledTimers[businessId];
      if (timer != null) {
        timer.cancel();
        _scheduledTimers.remove(businessId);
      }
      
      // Update notification status to cancelled
      final notification = await _repository.getNotificationByBusinessId(businessId);
      if (notification != null) {
        notification.status = NotificationStatus.cancelled;
        notification.updatedAt = DateTime.now();
        await _repository.updateNotification(notification);
        
        _logger.info('Cancelled scheduled notification: $businessId');
        return true;
      }
      
      return false;
    } catch (e, stackTrace) {
      _logger.severe('Error cancelling scheduled notification: $e', e, stackTrace);
      return false;
    }
  }
  
  /// Reschedule a notification
  Future<bool> rescheduleNotification(String businessId, DateTime newScheduledTime) async {
    try {
      // Cancel existing timer
      await cancelScheduledNotification(businessId);
      
      // Update notification with new scheduled time
      final notification = await _repository.getNotificationByBusinessId(businessId);
      if (notification != null) {
        notification.scheduledTime = newScheduledTime;
        notification.status = NotificationStatus.scheduled;
        notification.updatedAt = DateTime.now();
        await _repository.updateNotification(notification);
        
        // Schedule new timer
        _scheduleTimer(notification);
        
        _logger.info('Rescheduled notification: $businessId for $newScheduledTime');
        return true;
      }
      
      return false;
    } catch (e, stackTrace) {
      _logger.severe('Error rescheduling notification: $e', e, stackTrace);
      return false;
    }
  }
  
  /// Get all scheduled notifications
  Future<List<NotificationIsar>> getScheduledNotifications() async {
    try {
      final allNotifications = await _repository.getAllNotifications();
      return allNotifications
          .where((n) => n.status == NotificationStatus.scheduled)
          .toList();
    } catch (e, stackTrace) {
      _logger.severe('Error getting scheduled notifications: $e', e, stackTrace);
      return [];
    }
  }
  
  /// Initialize scheduling service and restore scheduled notifications
  Future<void> initialize() async {
    try {
      _logger.info('Initializing notification scheduling service');
      
      // Get all scheduled notifications from database
      final scheduledNotifications = await getScheduledNotifications();
      
      // Restore timers for future notifications
      for (final notification in scheduledNotifications) {
        if (notification.scheduledTime != null) {
          final now = DateTime.now();
          if (notification.scheduledTime!.isAfter(now)) {
            _scheduleTimer(notification);
          } else {
            // Notification is overdue, deliver immediately
            await _deliverOverdueNotification(notification);
          }
        }
      }
      
      _logger.info('Restored ${_scheduledTimers.length} scheduled notifications');
    } catch (e, stackTrace) {
      _logger.severe('Error initializing scheduling service: $e', e, stackTrace);
    }
  }
  
  /// Schedule a timer for a notification
  void _scheduleTimer(NotificationIsar notification) {
    if (notification.businessId == null || notification.scheduledTime == null) {
      return;
    }
    
    final now = DateTime.now();
    final scheduledTime = notification.scheduledTime!;
    
    if (scheduledTime.isBefore(now)) {
      // Deliver immediately if overdue
      _deliverOverdueNotification(notification);
      return;
    }
    
    final delay = scheduledTime.difference(now);
    final timer = Timer(delay, () async {
      await _deliverScheduledNotification(notification.businessId!);
    });
    
    _scheduledTimers[notification.businessId!] = timer;
  }
  
  /// Deliver a scheduled notification
  Future<void> _deliverScheduledNotification(String businessId) async {
    try {
      _scheduledTimers.remove(businessId);
      
      final notification = await _repository.getNotificationByBusinessId(businessId);
      if (notification == null) {
        _logger.warning('Scheduled notification not found: $businessId');
        return;
      }
      
      // Update status to pending delivery
      notification.status = NotificationStatus.pending;
      notification.updatedAt = DateTime.now();
      await _repository.updateNotification(notification);
      
      // Deliver the notification
      final delivered = await _deliveryService.deliverNotification(notification);
      
      // Update status based on delivery result
      notification.status = delivered ? NotificationStatus.delivered : NotificationStatus.failed;
      notification.deliveredAt = delivered ? DateTime.now() : null;
      notification.updatedAt = DateTime.now();
      await _repository.updateNotification(notification);
      
      _logger.info('Delivered scheduled notification: $businessId, success: $delivered');
      
    } catch (e, stackTrace) {
      _logger.severe('Error delivering scheduled notification: $e', e, stackTrace);
    }
  }
  
  /// Deliver an overdue notification
  Future<void> _deliverOverdueNotification(NotificationIsar notification) async {
    if (notification.businessId == null) return;
    
    _logger.warning('Delivering overdue notification: ${notification.businessId}');
    await _deliverScheduledNotification(notification.businessId!);
  }
  
  /// Generate a unique business ID
  String _generateBusinessId() {
    return 'scheduled_${DateTime.now().millisecondsSinceEpoch}_${_generateRandomString(6)}';
  }
  
  /// Generate a random string
  String _generateRandomString(int length) {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    final random = DateTime.now().millisecondsSinceEpoch;
    return List.generate(length, (index) => chars[(random + index) % chars.length]).join();
  }
  
  /// Cleanup resources
  void dispose() {
    for (final timer in _scheduledTimers.values) {
      timer.cancel();
    }
    _scheduledTimers.clear();
  }
}
