import 'dart:async';
import 'dart:convert';
import 'package:logging/logging.dart';
import 'package:get_it/get_it.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/notification_isar.dart';
import '../models/notification_status.dart';
import '../models/notification_priority.dart';
import 'notification_repository.dart';
import 'notification_delivery_service.dart';

/// Service for managing notifications when offline
class OfflineNotificationService {
  static final Logger _logger = Logger('OfflineNotificationService');
  final NotificationRepository _repository = GetIt.instance<NotificationRepository>();
  final NotificationDeliveryService _deliveryService = GetIt.instance<NotificationDeliveryService>();
  
  static const String _offlineQueueKey = 'offline_notification_queue';
  static const String _failedDeliveryKey = 'failed_delivery_queue';
  
  /// Queue a notification for offline delivery
  Future<void> queueOfflineNotification({
    required String title,
    required String message,
    String? category,
    String? cattleId,
    NotificationPriority priority = NotificationPriority.medium,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      final notification = {
        'businessId': _generateBusinessId(),
        'title': title,
        'message': message,
        'category': category ?? 'offline',
        'cattleId': cattleId,
        'priority': priority.name,
        'status': NotificationStatus.queued.name,
        'createdAt': DateTime.now().toIso8601String(),
        'additionalData': additionalData,
      };
      
      await _addToQueue(_offlineQueueKey, notification);
      _logger.info('Queued offline notification: ${notification['businessId']}');
      
    } catch (e, stackTrace) {
      _logger.severe('Error queuing offline notification: $e', e, stackTrace);
    }
  }
  
  /// Process offline notification queue when online
  Future<void> processOfflineQueue() async {
    try {
      _logger.info('Processing offline notification queue');
      
      final offlineQueue = await _getQueue(_offlineQueueKey);
      final failedQueue = await _getQueue(_failedDeliveryKey);
      
      // Process offline notifications
      for (final notificationData in offlineQueue) {
        await _processQueuedNotification(notificationData);
      }
      
      // Retry failed deliveries
      for (final notificationData in failedQueue) {
        await _retryFailedDelivery(notificationData);
      }
      
      // Clear processed queues
      await _clearQueue(_offlineQueueKey);
      await _clearQueue(_failedDeliveryKey);
      
      _logger.info('Processed ${offlineQueue.length + failedQueue.length} queued notifications');
      
    } catch (e, stackTrace) {
      _logger.severe('Error processing offline queue: $e', e, stackTrace);
    }
  }
  
  /// Process a single queued notification
  Future<void> _processQueuedNotification(Map<String, dynamic> notificationData) async {
    try {
      // Create notification object
      final notification = NotificationIsar()
        ..businessId = notificationData['businessId']
        ..title = notificationData['title']
        ..message = notificationData['message']
        ..category = notificationData['category']
        ..cattleId = notificationData['cattleId']
        ..priority = _parsePriority(notificationData['priority'])
        ..status = NotificationStatus.pending
        ..createdAt = DateTime.parse(notificationData['createdAt'])
        ..updatedAt = DateTime.now();
      
      // Save to database
      await _repository.createNotification(notification);
      
      // Attempt delivery
      final delivered = await _deliveryService.deliverNotification(notification);
      
      if (delivered) {
        notification.status = NotificationStatus.delivered;
        notification.deliveredAt = DateTime.now();
      } else {
        notification.status = NotificationStatus.failed;
        // Add to failed delivery queue for retry
        await _addToQueue(_failedDeliveryKey, notificationData);
      }
      
      notification.updatedAt = DateTime.now();
      await _repository.updateNotification(notification);
      
      _logger.info('Processed queued notification: ${notification.businessId}, delivered: $delivered');
      
    } catch (e, stackTrace) {
      _logger.severe('Error processing queued notification: $e', e, stackTrace);
    }
  }
  
  /// Retry failed delivery
  Future<void> _retryFailedDelivery(Map<String, dynamic> notificationData) async {
    try {
      final businessId = notificationData['businessId'];
      final notification = await _repository.getNotificationByBusinessId(businessId);
      
      if (notification != null) {
        notification.status = NotificationStatus.pending;
        notification.updatedAt = DateTime.now();
        await _repository.updateNotification(notification);
        
        final delivered = await _deliveryService.deliverNotification(notification);
        
        notification.status = delivered ? NotificationStatus.delivered : NotificationStatus.failed;
        if (delivered) {
          notification.deliveredAt = DateTime.now();
        }
        notification.updatedAt = DateTime.now();
        await _repository.updateNotification(notification);
        
        _logger.info('Retried failed delivery: $businessId, delivered: $delivered');
      }
      
    } catch (e, stackTrace) {
      _logger.severe('Error retrying failed delivery: $e', e, stackTrace);
    }
  }
  
  /// Add notification to queue
  Future<void> _addToQueue(String queueKey, Map<String, dynamic> notification) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final queueJson = prefs.getString(queueKey) ?? '[]';
      final queue = List<Map<String, dynamic>>.from(json.decode(queueJson));
      
      queue.add(notification);
      
      await prefs.setString(queueKey, json.encode(queue));
    } catch (e, stackTrace) {
      _logger.severe('Error adding to queue: $e', e, stackTrace);
    }
  }
  
  /// Get notification queue
  Future<List<Map<String, dynamic>>> _getQueue(String queueKey) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final queueJson = prefs.getString(queueKey) ?? '[]';
      return List<Map<String, dynamic>>.from(json.decode(queueJson));
    } catch (e, stackTrace) {
      _logger.severe('Error getting queue: $e', e, stackTrace);
      return [];
    }
  }
  
  /// Clear notification queue
  Future<void> _clearQueue(String queueKey) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(queueKey);
    } catch (e, stackTrace) {
      _logger.severe('Error clearing queue: $e', e, stackTrace);
    }
  }
  
  /// Get queue statistics
  Future<Map<String, int>> getQueueStats() async {
    try {
      final offlineQueue = await _getQueue(_offlineQueueKey);
      final failedQueue = await _getQueue(_failedDeliveryKey);
      
      return {
        'offlineQueued': offlineQueue.length,
        'failedDeliveries': failedQueue.length,
        'totalQueued': offlineQueue.length + failedQueue.length,
      };
    } catch (e, stackTrace) {
      _logger.severe('Error getting queue stats: $e', e, stackTrace);
      return {
        'offlineQueued': 0,
        'failedDeliveries': 0,
        'totalQueued': 0,
      };
    }
  }
  
  /// Clear all queues (for testing or reset)
  Future<void> clearAllQueues() async {
    await _clearQueue(_offlineQueueKey);
    await _clearQueue(_failedDeliveryKey);
    _logger.info('Cleared all notification queues');
  }
  
  /// Parse priority from string
  NotificationPriority _parsePriority(String? priorityString) {
    if (priorityString == null) return NotificationPriority.normal;
    
    try {
      return NotificationPriority.values.firstWhere(
        (p) => p.name == priorityString,
        orElse: () => NotificationPriority.medium,
      );
    } catch (e) {
      return NotificationPriority.medium;
    }
  }
  
  /// Generate a unique business ID
  String _generateBusinessId() {
    return 'offline_${DateTime.now().millisecondsSinceEpoch}_${_generateRandomString(6)}';
  }
  
  /// Generate a random string
  String _generateRandomString(int length) {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    final random = DateTime.now().millisecondsSinceEpoch;
    return List.generate(length, (index) => chars[(random + index) % chars.length]).join();
  }
}
