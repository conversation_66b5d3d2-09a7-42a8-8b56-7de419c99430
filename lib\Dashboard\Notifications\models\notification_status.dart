/// Enum representing notification status
enum NotificationStatus {
  /// Notification has not been read
  unread,

  /// Notification has been read
  read,

  /// Notification has been archived
  archived,

  /// Notification has been actioned (user took action on it)
  actioned,

  /// Notification is scheduled for future delivery
  scheduled,

  /// Notification is pending delivery
  pending,

  /// Notification has been delivered successfully
  delivered,

  /// Notification delivery failed
  failed,

  /// Notification has been cancelled
  cancelled,

  /// Notification is queued for offline delivery
  queued,

  /// Notification has expired
  expired
}